import React, { Suspense } from "react";
import EmployeesHeader from "../../_modules/people/_components/employees/header/employees-header";
import EmployeesTable from "../../_modules/people/_components/employees/table";

const Employees = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const params = await searchParams;
  return (
    <>
      <EmployeesHeader title="هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة" />
      <Suspense fallback={<div>{"loading... "}</div>}>
        <EmployeesTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Employees;
