import { Suspense } from "react";
import { EmployeeAttendanceCards } from "../../../_modules/people/_components/employees/profile";

export default async function EmployeeAttendancePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: employeeId } = await params;

  return (
    <div className="!mt-5">
      <Suspense fallback={<div>{"Loading..."}</div>}>
        <EmployeeAttendanceCards employeeId={employeeId} />
      </Suspense>
    </div>
  );
}
