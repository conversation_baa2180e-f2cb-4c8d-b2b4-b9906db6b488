"use client";

import React, { Suspense } from "react";
import { useEmployeeDetails } from "../../../_modules/people/hooks/employees/useEmployeeDetails";
import { useLocale } from "next-intl";
import { useParams } from "next/navigation";
import { Locale } from "@/i18n/routing";
import { EmployeeProfileLayout } from "../../../_modules/people/_components/employees/profile";
import { Skeleton } from "@/components/ui/skeleton";

export default function EmployeeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();

  const employeeId = params.id as string;
  const locale: Locale = useLocale() as Locale;
  const { employee, employeeData, salaryPackage, userRoles, isLoading, error } =
    useEmployeeDetails(employeeId);

  return (
    <Suspense
      fallback={
        <div className="p-6">
          <Skeleton className="h-[400px] w-full bg-gray-200" />
        </div>
      }
    >
      <EmployeeProfileLayout
        employeeId={employeeId}
        locale={locale}
        employee={employee}
        employeeData={employeeData}
        isLoading={isLoading}
        userRoles={userRoles}
        salaryPackage={salaryPackage}
        error={error ? error.message : null}
      >
        {children}
      </EmployeeProfileLayout>
    </Suspense>
  );
}
