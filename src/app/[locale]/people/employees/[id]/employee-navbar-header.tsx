"use client";

import { useParams, usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useEmployeeDetails } from "../../../_modules/people/hooks/employees/useEmployeeDetails";
import EmployeeProfileNavHeader from "../../../_modules/people/_components/employees/profile/employee-profile-nav-header";

export default function EmployeeNavbarHeader() {
  const pathname = usePathname();
  const { id } = useParams();
  const [employeeId, setEmployeeId] = useState<string>("");
  const { employee, error, isLoading } = useEmployeeDetails(employeeId);

  useEffect(() => {
    setEmployeeId(id as string);
  }, [pathname, id]);

  return (
    <div className="max-md:px-2">
      <EmployeeProfileNavHeader
        employeeName={
          error
            ? `Error: ${error.message || "Failed to load employee"}`
            : employee?.name || ""
        }
        isLoading={isLoading}
      />
    </div>
  );
}
