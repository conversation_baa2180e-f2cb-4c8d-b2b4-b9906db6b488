import React from "react";
import EmployeesCardWrapper from "../_modules/people/_components/cards/employees-card-wrapper";
import WavyArea<PERSON>hart from "../_modules/people/_components/area-chart-wrapper";
import DistributionOfLeavesChart from "../_modules/people/_components/distribution-of-leaves-chart";
import OrdersPercentageOver<PERSON>ear from "../_modules/people/_components/order-percentage-over-year";
import LatestLeavesRequests from "../_modules/people/_components/latest-leaves-requests";

const PeopleSystem = async () => {
  return (
    <div className={`max-md:mt-8 grid grid-cols-12 gap-4`}>
      <div
        className={`grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 col-span-12 font-readex_pro`}
      >
        <EmployeesCardWrapper />
      </div>
      <div className="border rounded-[20px] border-gray-200 col-span-12 max-h-[380px] font-readex_pro">
        <WavyAreaChart />
      </div>
      <div className="grid xl:grid-cols-[1.6fr_1fr] col-span-12 gap-4">
        <div className="border rounded-[20px] border-gray-200 max-h-[526px]">
          <DistributionOfLeavesChart />
        </div>
        <div className="border rounded-[20px] border-gray-200 max-h-[530px]">
          <LatestLeavesRequests />
        </div>
      </div>
      <div className="border rounded-[20px] border-gray-200 col-span-12 max-h-[380px]">
        <OrdersPercentageOverYear />
      </div>
    </div>
  );
};

export default PeopleSystem;
