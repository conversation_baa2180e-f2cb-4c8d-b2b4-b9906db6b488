import React, { Suspense } from "react";
import LeaveRequestsTable from "../../_modules/people/_components/leave-requests/table";

const Requests = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const params = await searchParams;
  return (
    <>
      <RequestsHeader title="هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة" />
      <Suspense fallback={<div>Loading...</div>}>
        <LeaveRequestsTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Requests;

const RequestsHeader = ({ title }: { title: string }) => {
  return (
    <h2 className="max-w-[301px] leading-[120%] text-base text-secondary mb-0.5">
      {title}
    </h2>
  );
};
