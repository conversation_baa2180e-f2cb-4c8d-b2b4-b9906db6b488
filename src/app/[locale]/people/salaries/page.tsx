import React, { Suspense } from "react";
import EmployeesSalariesTable from "../../_modules/people/_components/employees-salaries/table";

const Salaries = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const params = await searchParams;
  return (
    <>
      <SalariesHeader title="هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة" />
      <Suspense fallback={<div>Loading...</div>}>
        <EmployeesSalariesTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Salaries;

const SalariesHeader = ({ title }: { title: string }) => {
  return (
    <h2 className="max-w-[301px] leading-[120%] text-base text-secondary mb-0.5">
      {title}
    </h2>
  );
};
