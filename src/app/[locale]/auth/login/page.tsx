import CardWrapper from "@/components/auth/card-wrapper";
import { getTranslations } from "next-intl/server";
import LoginForm from "@/app/[locale]/_components/auth/LoginForm";
import React from "react";

const Login = async () => {
  const t = await getTranslations("auth");
  return (
    <CardWrapper
      headerTitle={t("login.title")}
      headerLabel={t("login.description")}
    >
      <LoginForm />
    </CardWrapper>
  );
};

export default Login;
