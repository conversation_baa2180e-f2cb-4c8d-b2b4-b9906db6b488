import useS<PERSON> from "swr";
import { fetcher } from "@/services/fetcher";
import { TEmployeesResponse } from "../../type/employee";
import { useSearchParams } from "next/navigation";

export const useEmployees = (
  page: number,
  limit: number,
  sortBy: string = "start_date",
) => {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get("search") || "";
  const filters = searchParams.get("filters") || "";

  // When searching, we still want to use pagination
  const effectivePage = page;
  const effectiveLimit = limit;
  // Build the API URL with all necessary parameters
  let apiUrl = `/api/employees?sort=${sortBy}&page=${effectivePage}&limit=${effectiveLimit}${
    searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ""
  }`;

  // Add filter parameters directly to the URL
  if (filters) {
    try {
      // Parse the filters parameter
      const filterParams = new URLSearchParams(filters);

      // Add each filter parameter directly to the API URL
      filterParams.forEach((value, key) => {
        // Only include actual filter parameters, not the group parameters
        if (key.startsWith("filter[") && !key.startsWith("group[")) {
          apiUrl += `&${key}=${encodeURIComponent(value)}`;
        }
      });
    } catch (error) {
      console.error("Error parsing filters:", error);
    }
  }

  const { data, error, isLoading, mutate } = useSWR<TEmployeesResponse>(
    apiUrl,
    fetcher,
  );

  const from = data?.meta?.pagination?.from;
  const to = data?.meta?.pagination?.to;

  return {
    employees: data?.data ?? [],
    totalCount: data?.meta?.pagination?.count ?? 0,
    isLoading,
    error,
    mutate,
    pagination: {
      firstResult: from,
      lastResult: to,
      limit: Number(limit),
      page: Number(page),
    },
  };
};
