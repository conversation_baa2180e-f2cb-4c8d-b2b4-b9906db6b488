import useSWR from "swr";
import { EmployeeAttendanceAttributes } from "../../type/employee";
import { fetcher } from "@/services/fetcher";
import { AttendanceMeta, AttendancePeriod } from "@/types/employee/daySummary";

interface EmployeeStatisticsResponse {
  data: {
    employee_id: number;
    period: {
      start_date: string; // ISO date string (e.g., "2025-05-01")
      end_date: string;
    };
    statistics: {
      leaves_taken: number;
      arrived_late: number;
      departed_early: number;
      absent: number;
    };
  };
}

export const useGetEmployeeMonthStatistics = (employeeId: string) => {
  const { data, error, isLoading, mutate } = useSWR<EmployeeStatisticsResponse>(
    employeeId
      ? `/api/employees/${employeeId}/attendance_periods/month_statistics`
      : null,
    fetcher
  );

  return {
    monthStatistics: data?.data,
    isLoading,
    error,
    mutate,
  };
};
