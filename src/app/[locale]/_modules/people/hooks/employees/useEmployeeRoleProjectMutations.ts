"use client";

import { useState } from "react";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useTranslations } from "next-intl";
import { deleteUserRole } from "../../actions/employee-role-project-actions";

type UseEmployeeRoleProjectMutationsProps = {
  employeeId: string;
  onSuccess?: () => void;
};

export const useEmployeeRoleProjectMutations = ({
  employeeId,
  onSuccess,
}: UseEmployeeRoleProjectMutationsProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { showToast } = useToastMessage();
  const t = useTranslations();

  // Delete role project
  const deleteRoleProject = async (userRoleId: string) => {
    if (!employeeId || !userRoleId) return;

    try {
      setIsDeleting(true);

      // Call the server action to delete the user role
      const result = await deleteUserRole(employeeId, userRoleId);

      // If the deletion failed, throw an error
      if (!result.success) {
        throw new Error(result.error);
      }

      showToast(
        "success",
        t("people.employees-page.profile.roles-projects.delete.success"),
      );

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error deleting role project:", error);
      showToast(
        "error",
        t("people.employees-page.profile.roles-projects.delete.error"),
      );
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    deleteRoleProject,
    isDeleting,
  };
};
