import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { MetricCardResponse } from "@/types";
import { endOfMonth, format, startOfMonth } from "date-fns";

export const useStatistic = (
  employeeId: string,
  metricArray?: string[],
  period: string = "month",
  startDate: string = format(startOfMonth(new Date()), "dd-MM-yyyy"),
  endDate?: string,
) => {
  const metricsParams =
    metricArray && metricArray.length > 0
      ? metricArray.map((ele) => `filter[metric_key_in][]=${ele}`).join("&")
      : "";

  const params = [
    `context[comparison_period]=${period}`,
    `context[employee_id]=${employeeId}`,
    `context[start_date]=${startDate}`,
    endDate ? `context[end_date]=${endDate}` : "",
    metricsParams,
  ]
    .filter(Boolean)
    .join("&");

  const url = employeeId ? `/api/statistics?${params}` : "";

  const { data, error, isLoading, mutate } = useSWR<MetricCardResponse>(
    url,
    fetcher,
  );
  return {
    metricCard: data?.data,
    isLoading,
    error,
    mutate,
  };
};
