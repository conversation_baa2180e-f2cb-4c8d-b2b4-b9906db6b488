import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { TEmployeeAttachmentsResponse } from "../type/employee";

export const useLeaveAttachments = (
  leaveId: string,
  page: number = 1,
  limit: number = 20,
) => {
  const { data, error, isLoading, mutate } =
    useSWR<TEmployeeAttachmentsResponse>(
      leaveId
        ? `/api/leaves/${leaveId}/documents?page=${page}&limit=${limit}`
        : null,
      fetcher,
      {
        revalidateOnFocus: false,
        dedupingInterval: 5000,
      },
    );

  return {
    attachments: data?.data || [],
    pagination: data?.meta?.pagination,
    isLoading,
    error,
    mutate,
  };
};
