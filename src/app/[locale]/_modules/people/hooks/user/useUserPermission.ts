import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { Permission } from "@/types/auth";

interface PermissionsResponse {
  data: Permission[];
  meta: {
    scope: string;
    count: number;
  };
}

export const useUserPermission = () => {
  const { data, error, isLoading, mutate } = useSWR<PermissionsResponse>(
    `/api/users/me/permissions`,
    fetcher,
  );

  return {
    permission: data?.data,
    isLoading,
    error,
    mutate,
  };
};
