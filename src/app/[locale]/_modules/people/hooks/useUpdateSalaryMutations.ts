import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import { fetchSalariesData } from "@/constants";
import { updateSalaryStatus } from "../actions/salaries";
import { TEmployeesSalariesResponse } from "../type/employees-salaries";

interface useUpdateSalaryStatusMutationsProps {
  key: string; // API Key for SWR
}

export const useUpdateSalaryStatusMutations = ({
  key,
}: useUpdateSalaryStatusMutationsProps) => {
  return useOptimisticMutation<
    TEmployeesSalariesResponse,
    { id: string; status: "paid" | "rejected" },
    TEmployeesSalariesResponse
  >({
    key,
    fetcher: fetchSalariesData,
    mutations: {
      updateSalaryStatus: {
        updateFn: (data, { id, status }) =>
          data
            ? {
                ...data,
                salariesData: data.salariesData.map((salary) =>
                  salary.id === id ? { ...salary, status } : salary,
                ),
              }
            : data,
        mutationFn: updateSalaryStatus,
      },
    },
    defaultData: { salariesData: [], totalCount: 0 },
    swrOptions: {
      dedupingInterval: 2000,
      revalidateAfterMutation: true,
    },
  });
};
