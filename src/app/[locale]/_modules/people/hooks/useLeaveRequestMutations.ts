import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import { useLeaveApprovalMutations } from "./useLeaveApprovalMutations";
import { fetcher } from "@/services/fetcher";
import { EmployeeLeavesResponse, LeaveDetail } from "../type/employee-leaves";

interface UseLeaveRequestMutationsProps {
  key: string;
}

// Define the parameter type for mutations
type MutationParams =
  | string
  | {
      id: string;
      leaveId?: string; // ID of the leave for optimistic updates
      comment?: string;
      approvalRequestId?: string;
      employeeId?: string;
    };

export const useLeaveRequestMutations = ({
  key,
}: UseLeaveRequestMutationsProps) => {
  // Use the unified approval mutations hook
  const {
    approveLeaveRequest,
    rejectLeaveRequest,
    withdrawLeaveRequest,
    isApproving,
    isRejecting,
    isWithdrawing,
  } = useLeaveApprovalMutations();
  const result = useOptimisticMutation<
    EmployeeLeavesResponse,
    MutationParams,
    EmployeeLeavesResponse
  >({
    key,
    fetcher: fetcher,
    mutations: {
      acceptLeaveRequest: {
        updateFn: (data, params) => {
          // For optimistic updates, use leaveId if available, otherwise use id
          const leaveId =
            typeof params === "string" ? params : params.leaveId || params.id;
          if (!data) return data;

          return {
            ...data,
            data: data.data.map((leave: LeaveDetail) =>
              leave.id === leaveId
                ? {
                    ...leave,
                    attributes: {
                      ...leave.attributes,
                      status: "accepted",
                    },
                  }
                : leave,
            ),
          };
        },
        mutationFn: async (params) => {
          const id = typeof params === "string" ? params : params.id;
          const comment =
            typeof params === "string" ? "" : params.comment || "";

          try {
            await approveLeaveRequest(id, comment);

            // Return empty data structure that matches EmployeeLeavesResponse
            return {
              data: {
                data: [],
                meta: {
                  pagination: {
                    count: 0,
                    page: 1,
                    limit: 10,
                    from: 0,
                    to: 0,
                  },
                },
              },
            };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to approve leave request"),
            };
          }
        },
      },
      rejectLeaveRequest: {
        updateFn: (data, params) => {
          // For optimistic updates, use leaveId if available, otherwise use id
          const leaveId =
            typeof params === "string" ? params : params.leaveId || params.id;
          if (!data) return data;

          return {
            ...data,
            data: data.data.map((leave: LeaveDetail) =>
              leave.id === leaveId
                ? {
                    ...leave,
                    attributes: {
                      ...leave.attributes,
                      status: "rejected",
                    },
                  }
                : leave,
            ),
          };
        },
        mutationFn: async (params) => {
          const id = typeof params === "string" ? params : params.id;
          const comment =
            typeof params === "string" ? "" : params.comment || "";

          try {
            await rejectLeaveRequest(id, comment);

            // Return empty data structure that matches EmployeeLeavesResponse
            return {
              data: {
                data: [],
                meta: {
                  pagination: {
                    count: 0,
                    page: 1,
                    limit: 10,
                    from: 0,
                    to: 0,
                  },
                },
              },
            };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to reject leave request"),
            };
          }
        },
      },
      withdrawLeaveRequest: {
        updateFn: (data, params) => {
          // For optimistic updates, use leaveId if available, otherwise use id
          const leaveId =
            typeof params === "string" ? params : params.leaveId || params.id;
          if (!data) return data;

          return {
            ...data,
            data: data.data.map((leave: LeaveDetail) =>
              leave.id === leaveId
                ? {
                    ...leave,
                    attributes: {
                      ...leave.attributes,
                      status: "withdrawn",
                    },
                  }
                : leave,
            ),
          };
        },
        mutationFn: async (params) => {
          const id = typeof params === "string" ? params : params.id;
          const employeeId =
            typeof params === "string" ? undefined : params.employeeId;

          try {
            if (!employeeId) {
              throw new Error(
                "Employee ID is required for withdrawing leave request",
              );
            }

            await withdrawLeaveRequest(id, employeeId);

            // Return empty data structure that matches EmployeeLeavesResponse
            return {
              data: {
                data: [],
                meta: {
                  pagination: {
                    count: 0,
                    page: 1,
                    limit: 10,
                    from: 0,
                    to: 0,
                  },
                },
              },
            };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to withdraw leave request"),
            };
          }
        },
      },
    },
    defaultData: {
      data: [],
      meta: {
        pagination: {
          count: 0,
          page: 1,
          limit: 10,
          from: 0,
          to: 0,
        },
      },
    },
    swrOptions: {
      dedupingInterval: 2000,
      revalidateAfterMutation: true,
    },
  });
  return {
    ...result,
    isApproving,
    isRejecting,
    isWithdrawing,
  };
};
