import { TEmployeeData } from "./employee";

//legacy type for backward compatibility
export type TEmployeesSalaries = {
  id: string;
  employeeName: string;
  month: string;
  totalHours: number;
  totalSalary: number;
  status: string;
  paymentDate: string | null;
  numberOfLeaves: number;
  overtimeHours: number;
  hourlyRate: number;
  totalDiscount: number;
  tax: number;
  notes?: string;
};

//legacy type for backward compatibility
export type TEmployeesSalariesResponse = {
  salariesData: TEmployeesSalaries[];
  totalCount: number;
};

export type SalaryCalculationApiResponse = {
  data: SalaryCalculation[];
  included: TEmployeeData[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type SalaryCalculation = {
  id: string;
  type: string;
  employee: TEmployeeData;
  attributes: {
    period: string;
    period_start_date: string;
    period_end_date: string;
    gross_salary: string;
    total_hours?: number;
    deductions: {
      leave: number;
      other: number;
      attendance: number;
      income_tax: string;
      social_security: string;
    };
    net_salary: string;
    status: string;
    calculation_date: string;
    payment_date: string | null;
    notes: string | null;
    created_at: string;
    updated_at: string;
    total_deductions: number;
    has_salary_slip: boolean;
  };
  relationships: {
    employee: {
      data: {
        id: string;
        type: string;
      };
    };
    salary_package: {
      data: {
        id: string;
        type: string;
      };
    };
    approval_request?: {
      data: {
        id: string;
        type: string;
      };
    };
  };
};
