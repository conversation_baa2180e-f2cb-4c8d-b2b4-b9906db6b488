"use client";

import useS<PERSON> from "swr";
import { fetchCardData } from "../../services/api/employees-card";
import { useTranslations } from "next-intl";
import { CardsSkeleton } from "@/components/skeletons";

const fetcher = () => fetchCardData().then((data) => data);

const EmployeesCardWrapper = () => {
    const t = useTranslations();
    const { data, error, isLoading, isValidating } = useSWR("employees", fetcher);

    if (error) {
        return (
            <div className="min-h-[172px] w-full font-semibold rounded-2xl border border-gray-200 bg-white p-6 flex items-center justify-center text-error">
                {error.message} (Status: {error.statusCode})
            </div>
        );
    }

    // If data is not available, or if it's still fetching, show the skeleton loader.
    if (!data || isLoading || isValidating) {
        return (
            <CardsSkeleton
                count={4}
                hasIcon={false}
                hasDetail={true}
                className="min-h-[172px]"
                titleClassName="h-6"
                valueClassName="min-h-8 mb-1"
                subtextClassName="min-h-8 mb-1"
                detailClassName="min-h-[42px] w-full"
            />
        );
    }

    // Destructure data when available.
    const { attendanceRate, averageDailyHours, pendingRequests, totalEmployees } =
        data;

    return (
        <>
            <EmployeesCard
                title={t("people.card.totalEmployees.title")}
                value={totalEmployees}
                subTitle={t("people.card.totalEmployees.subTitle")}
                detail={t("people.card.totalEmployees.detail")}
            />
            <EmployeesCard
                title={t("people.card.attendanceRate.title")}
                value={attendanceRate}
                subTitle={t("people.card.attendanceRate.subTitle")}
                detail={t("people.card.attendanceRate.detail")}
            />
            <EmployeesCard
                title={t("people.card.averageDailyHours.title")}
                value={averageDailyHours}
                subTitle={t("people.card.averageDailyHours.subTitle")}
                detail={t("people.card.averageDailyHours.detail")}
            />
            <EmployeesCard
                title={t("people.card.pendingRequests.title")}
                value={pendingRequests}
                subTitle={t("people.card.pendingRequests.subTitle")}
                detail={t("people.card.pendingRequests.detail")}
            />
        </>
    );
};

export default EmployeesCardWrapper;

type TCard = {
    title: string;
    subTitle: string;
    value: number | string;
    detail: string;
};

export const EmployeesCard = ({ title, subTitle, value, detail }: TCard) => {
    return (
        <div className="rounded-2xl border border-gray-200 bg-white p-6 max-h-[172px]">
            <div className="flex flex-col">
                <h3 className="font-medium leading-n text-base text-gray-700 mb-2">
                    {title}
                </h3>
                <p className="font-bold leading-8 text-2xl mb-3 flex items-center gap-2">
                    <span className="inline-block">{value}</span>
                    <span className="text-gray-400 inline-block">{subTitle}</span>
                </p>
                <p className="text-base font-normal leading-6 text-gray-400">
                    {detail}
                </p>
            </div>
        </div>
    );
};
