"use client";

import { PaginationWithLinks } from "@/components/pagination-with-links";
import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { columns } from "./employees-columns";
import { RowSelectionState } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { useEmployees } from "../../../hooks/employees/useEmployees";
import { useRouter } from "@/i18n/routing";

type EmployeesTableProps = {
  showPagination: boolean;
  searchParams: {
    page: string;
    limit: string;
  };
};

const EmployeesTable = ({
  showPagination,
  searchParams,
}: EmployeesTableProps) => {
  const t = useTranslations();
  const router = useRouter();

  const limit = parseInt(searchParams.limit ?? 5, 10);
  const page = parseInt(searchParams.page ?? 1, 10);
  const { employees, totalCount, pagination, isLoading, error } = useEmployees(
    page,
    limit,
    "start_date",
  );

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  return (
    <>
      <DataTable
        data={employees}
        dataCount={totalCount}
        columns={columns}
        tableContainerClass="min-h-[240px] text-start"
        title={t("people.employees-page.table.title")}
        meta={{}}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.employees-page.table"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        tableHeadStyle="text-start"
        tableCellStyle="text-start"
        onRowClick={(row) => {
          const employeeId = row.attributes.user_id;
          employeeId && router.push(`/people/employees/${employeeId}`);
        }}
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={pagination.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount || 0)}
            firstLastCounts={{
              firstCount: pagination.firstResult ?? 1,
              lastCount: pagination.lastResult ?? 3,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!employees?.length}
          />
        </div>
      )}
    </>
  );
};

export default EmployeesTable;
