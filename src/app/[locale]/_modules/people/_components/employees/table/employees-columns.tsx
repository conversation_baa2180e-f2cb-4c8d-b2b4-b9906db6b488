"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import { EmployeesAction } from "./employees-actions";
import { Locale } from "@/i18n/routing";
import { formatDate } from "@/lib/dateFormatter";
import { TFunction } from "@/types";
import { TEmployeeData } from "../../../type/employee";

interface TableMetaWithTranslation extends TableMeta<TEmployeeData> {
  t: TFunction;
  locale?: Locale;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getMeta = (table: any) => table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<TEmployeeData>[] = [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACB5BB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) =>
          table.toggleAllPageRowsSelected(Boolean(value))
        }
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(Boolean(value))}
        aria-label="Select row"
      />
    ),
  },

  {
    id: "number",
    accessorKey: "id",
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
    header: ({ table }) => getMeta(table).t("cm.table.columns.number"),
    cell: ({ row }) => {
      return <p className="text-gray-500">{row.getValue("number")}</p>;
    },
  },

  {
    accessorKey: "name",
    id: "employeeName",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
      filterVariant: "text",
    },
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t("people.employees-page.table.columns.employeeName")}
      </div>
    ),
    size: 100,
    cell: ({ row }) => {
      const employee = row.original;
      const name = employee.attributes.name;
      return (
        <p className="text-sm font-semibold text-black text-start">{name}</p>
      );
    },
  },

  {
    accessorKey: "department_name",
    id: "jobTitle",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
      filterVariant: "text",
    },
    header: ({ table }) => (
      <div>
        {getMeta(table).t("people.employees-page.table.columns.jobTitle")}
      </div>
    ),
    cell: ({ row }) => {
      const employee = row.original;

      const departmentName = employee.attributes.department_name;

      return (
        <p className="text-sm font-semibold text-gray-500">{departmentName}</p>
      );
    },
  },
  {
    accessorKey: "start_date",
    id: "registrationDate",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => (
      <div>
        {getMeta(table).t(
          "people.employees-page.table.columns.registrationDate"
        )}
      </div>
    ),
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const employee = row.original;
      const dateStr = employee.attributes.start_date;
      const formattedDate = dateStr ? formatDate(dateStr, locale ?? "ar") : "-";
      return (
        <p className="text-sm font-semibold text-gray-500">{formattedDate}</p>
      );
    },
  },

  {
    accessorKey: "actions",
    id: "actions",
    enableHiding: false,
    enableColumnFilter: false,
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("cm.table.columns.actions")}
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-center">
        <EmployeesAction row={row} />
      </div>
    ),
  },
];
