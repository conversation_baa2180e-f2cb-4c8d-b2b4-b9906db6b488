import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LANGUAGES } from "@/constants/enum";
import { Row } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { CiMenuKebab } from "react-icons/ci";
import { Locale } from "@/i18n/routing";
import { LeaveDetail } from "../../../../type/employee-leaves";
import { useState } from "react";
import { UpdateDatesModal } from "./update-dates-modal";
import { Loader } from "lucide-react";

type EmployeeLeavesActionsProps<TData> = {
  row: Row<TData>;
  onWithdrawLeave?: (leaveId: string) => void;
  onUpdateDates?: (
    leaveId: string,
    startDate: Date,
    endDate: Date,
    leaveDuration?: string,
    leaveType?: string,
  ) => void;
  onAcceptLeave?: (leaveId: string) => void;
  onRejectLeave?: (leaveId: string) => void;
  isWithdrawing?: boolean;
  isUpdatingDates?: boolean;
  isAccepting?: boolean;
  isRejecting?: boolean;
};

const EmployeeLeavesActions = <TData extends LeaveDetail>({
  row,
  onWithdrawLeave,
  onUpdateDates,
  onAcceptLeave,
  onRejectLeave,
  isWithdrawing,
  isUpdatingDates,
  isAccepting,
  isRejecting,
}: EmployeeLeavesActionsProps<TData>) => {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();

  // State for the update dates modal
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  // Get the leave details
  const leave = row.original;
  const startDate = leave.attributes.start_date;
  const endDate = leave.attributes.end_date;
  const status = leave.attributes.status;
  const leaveType = leave.attributes.leave_type;
  const leaveDuration = leave.attributes.leave_duration;

  // Check if the end date hasn't expired
  const isEndDateValid = endDate ? new Date(endDate) >= new Date() : false;

  const canWithdraw =
    status === "waiting" ||
    (status === "pending" && isEndDateValid) ||
    (status === "approved" && isEndDateValid);

  const canUpdateDates =
    status === "waiting" || (status === "pending" && isEndDateValid);

  const canAccept = status === "waiting" || status === "pending";

  const canReject = status === "waiting" || status === "pending";

  const handleUpdateDates = (newStartDate: Date, newEndDate: Date) => {
    if (onUpdateDates) {
      onUpdateDates(
        leave.id,
        newStartDate,
        newEndDate,
        leaveDuration,
        leaveType,
      );
      setIsUpdateModalOpen(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="p-2 rounded-full hover:bg-gray-100">
            <CiMenuKebab className="h-4 w-4 text-gray-500" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          lang={locale}
          align={isAr ? "start" : "end"}
          className="w-60 max-w-60 rounded-2xl space-y-1 font-semibold text-sm text-start text-[#727A90] p-2"
        >
          {canAccept && (
            <DropdownMenuItem
              className="cursor-pointer text-gray-400 hover:bg-background-v2 hover:text-green-500 rtl:justify-end"
              onSelect={() => {
                if (onAcceptLeave && !isAccepting) {
                  onAcceptLeave(leave.id);
                }
              }}
              disabled={isAccepting}
            >
              <span className="text-sm font-semibold text-end">
                {isAccepting ? (
                  <Loader className="animate-spin !w-4 !h-4" />
                ) : (
                  t("people.leaves-requests-component.actions.approve")
                )}
              </span>
            </DropdownMenuItem>
          )}

          {canReject && (
            <DropdownMenuItem
              className="cursor-pointer text-gray-400 hover:bg-background-v2 hover:text-error rtl:justify-end"
              onSelect={() => {
                if (onRejectLeave && !isRejecting) {
                  onRejectLeave(leave.id);
                }
              }}
              disabled={isRejecting}
            >
              <span className="text-sm font-semibold text-end">
                {isRejecting ? (
                  <Loader className="animate-spin !w-4 !h-4" />
                ) : (
                  t("people.leaves-requests-component.actions.reject")
                )}
              </span>
            </DropdownMenuItem>
          )}

          {canUpdateDates && (
            <DropdownMenuItem
              className="cursor-pointer text-gray-400 hover:bg-background-v2 hover:text-secondary rtl:justify-end"
              onSelect={() => {
                if (!isUpdatingDates) {
                  setIsUpdateModalOpen(true);
                }
              }}
              disabled={isUpdatingDates}
            >
              <span className="text-sm font-semibold text-end">
                {isUpdatingDates ? (
                  <Loader className="animate-spin !w-4 !h-4" />
                ) : (
                  t(
                    "people.employees-page.profile.leaves.update-dates.update-button",
                  )
                )}
              </span>
            </DropdownMenuItem>
          )}

          {canWithdraw && (
            <DropdownMenuItem
              className="cursor-pointer text-gray-400 hover:text-error hover:bg-background-v2 rtl:justify-end"
              onSelect={() => {
                if (onWithdrawLeave && !isWithdrawing) {
                  onWithdrawLeave(leave.id);
                }
              }}
              disabled={isWithdrawing}
            >
              <span className="text-sm font-semibold text-end">
                {isWithdrawing ? (
                  <Loader className="animate-spin" />
                ) : (
                  t(
                    "people.employees-page.profile.leaves.table.actions.withdraw",
                  )
                )}
              </span>
            </DropdownMenuItem>
          )}

          {!canUpdateDates && !canWithdraw && !canAccept && !canReject && (
            <DropdownMenuItem
              className="cursor-default text-gray-400 rtl:justify-end"
              disabled
            >
              <span className="text-sm font-semibold text-end">
                {t("common.NoActionsAvailable")}
              </span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Date picker modal */}
      <UpdateDatesModal
        isOpen={isUpdateModalOpen}
        onClose={() => setIsUpdateModalOpen(false)}
        onUpdate={handleUpdateDates}
        initialStartDate={startDate}
        initialEndDate={endDate}
        isLoading={isUpdatingDates || false}
      />
    </>
  );
};

export { EmployeeLeavesActions };
