import { TUserRoleIncluded } from "../type/employee";
import { TRoleProjectPair } from "../type/employee-roles-projects";

export function transformUserRolesToRoleProjectPairs(
  userRoles: TUserRoleIncluded[],
): TRoleProjectPair[] {
  return userRoles
    .filter((userRole) => userRole.type === "user_role") // Ensure we're only processing user_role items
    .map((userRole) => {
      // Get the role and project IDs from the user role relationships
      const roleId = userRole.relationships?.role?.data?.id;
      const projectId = userRole.relationships?.project?.data?.id;

      if (!roleId || !projectId) return null;

      // Find the role and project data in the included array
      const roleData = userRoles.find(
        (item) => item.type === "role" && item.id === roleId,
      );

      const projectData = userRoles.find(
        (item) => item.type === "project" && item.id === projectId,
      );

      if (!roleData || !projectData) return null;

      // Extract role name and project name safely
      let roleName = "";
      let projectName = "";
      let isDefault = userRole.attributes?.is_default || false;

      if (roleData.attributes && typeof roleData.attributes === "object") {
        // Use type assertion to access the name property
        const roleAttrs = roleData.attributes as { name?: string };
        roleName = roleAttrs.name || "";
      }

      if (
        projectData.attributes &&
        typeof projectData.attributes === "object"
      ) {
        // Use type assertion to access the name property
        const projectAttrs = projectData.attributes as { name?: string };
        projectName = projectAttrs.name || "";
      }

      return {
        id: userRole.id, // Add id for DataTable compatibility
        userRoleId: userRole.id,
        roleId,
        projectId,
        roleName,
        projectName,
        isDefault,
      };
    })
    .filter(
      (item): item is TRoleProjectPair =>
        item !== null && Boolean(item.roleName) && Boolean(item.projectName),
    );
}
