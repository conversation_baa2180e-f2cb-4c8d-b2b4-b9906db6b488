// src/app/[locale]/_modules/people/actions/employee-actions.ts
"use server";

import { isValidationSuccess, validateFormData } from "@/lib/form-utils";
import { ActionState } from "@/types";
import { EmployeeSchemaType, employeeSchema } from "../schemas/employeeSchema";
import { peopleService } from "@/services/api/people";
import { TEmployee } from "../type/employee";
import { handleError } from "@/lib/utils";

export async function addEmployee(
  _prevState: ActionState<TEmployee>,
  formData: FormData,
): Promise<ActionState<TEmployee>> {
  try {
    // Validate from data using zod schema
    // Use type assertion to work around the type mismatch
    const validation = (await validateFormData(formData, employeeSchema)) as
      | { success: true; data: EmployeeSchemaType }
      | ActionState<EmployeeSchemaType>;
    if (!isValidationSuccess<EmployeeSchemaType>(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
        data: null,
      } as ActionState<TEmployee>;
    }

    const addNewEmployee = await peopleService.addNewEmployee(formData);

    return {
      success: "Employee added successfully",
      error: null,
      issues: null,
      data: addNewEmployee,
    };
  } catch (error) {
    return handleError(error, "Failed to add employee");
  }
}
