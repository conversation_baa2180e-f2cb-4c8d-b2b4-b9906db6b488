"use server";

import { peopleService } from "@/services/api/people";
import { ActionState } from "@/types";
import {
  createLeaveSchema,
  CreateLeaveSchemaType,
} from "../schemas/leaveSchema";
import { handleError } from "@/lib/utils";
import { isValidationSuccess, validateFormData } from "@/lib/form-utils";

export async function createLeave(
  employeeId: string,
  _prevState: ActionState<{ id: string; status: string }>,
  formData: FormData,
): Promise<ActionState<{ id: string; status: string }>> {
  try {
    if (!employeeId) {
      return {
        error: "Employee ID is required",
        success: "",
        issues: [],
        data: null,
      };
    }

    const validation = (await validateFormData(formData, createLeaveSchema)) as
      | { success: true; data: CreateLeaveSchemaType }
      | ActionState<CreateLeaveSchemaType>;
    if (!isValidationSuccess<CreateLeaveSchemaType>(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
        data: null,
      } as ActionState<{ id: string; status: string }>;
    }

    // Create leave request
    const response = await peopleService.createLeave(employeeId, formData);

    // Type assertion for response
    const typedResponse = response as { data?: { id: string } };

    return {
      success: "Leave request created successfully",
      error: "",
      issues: [],
      data: {
        id: typedResponse.data?.id || "",
        status: "created",
      },
    };
  } catch (err) {
    return handleError(err, "Failed to create leave request", []);
  }
}
