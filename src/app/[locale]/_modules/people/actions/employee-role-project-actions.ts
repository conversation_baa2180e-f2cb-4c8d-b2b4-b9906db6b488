"use server";

import { peopleService } from "@/services/api/people";

export async function deleteUserRole(employeeId: string, userRoleId: string) {
  try {
    const result = await peopleService.deleteUserRole(employeeId, userRoleId);

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("Error deleting user role:", error);
    return {
      success: false,
      error: "Failed to delete user role",
    };
  }
}
