// schemas/salarySchema.ts
import { z } from "zod";
import { TFunction } from "@/types";

export const salarySchema = (t: TFunction, mode: "salary" | "note") => {
  if (mode === "salary") {
    return z.object({
      totalSalary: z
        .string()
        .min(1, { message: t("common.form.updateSalary.salary-required") })
        .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
          message: t("common.form.verifyCode.error.notNumber"),
        }),
      note: z.string().optional(),
    });
  }
  return z.object({
    totalSalary: z
      .string()
      .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
        message: t("common.form.verifyCode.error.notNumber"),
      })
      .optional(),
    note: z
      .string()
      .min(1, { message: t("common.form.updateSalary.note-required") }),
  });
};

export type SalarySchemaType = z.infer<ReturnType<typeof salarySchema>>;
