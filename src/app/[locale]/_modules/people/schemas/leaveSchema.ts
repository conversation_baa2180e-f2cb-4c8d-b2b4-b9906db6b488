// src/app/[locale]/_modules/people/schemas/leaveSchema.ts
import { z } from "zod";
import { TFunction } from "@/types";
import { LEAVE_DURATION, LEAVE_TYPE } from "../enum";
import { MAX_FILE_SIZE } from "../constants";

// Common date validation functions
const createDateValidators = () => {
  // Get current date and set to midnight
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Calculate date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
  ninetyDaysAgo.setHours(0, 0, 0, 0);

  return {
    validateDateFormat: (date: Date) => !isNaN(date.getTime()),
    validateStartDateNotTooOld: (date: Date) => date >= ninetyDaysAgo,
    validateEndDateNotInPast: (date: Date) => date >= today,
    validateStartBeforeEnd: (startDate: Date, endDate: Date) =>
      startDate <= endDate,
  };
};

// Schema for creating a new leave request
export const createLeaveSchema = (t: TFunction) => {
  const validators = createDateValidators();
  const attachmentFileSchema = z
    .instanceof(File)
    .refine((file) => file.size < MAX_FILE_SIZE, {
      message: t("common.form.attachment.error.file.maxSize"),
    });

  return z
    .object({
      employeeId: z
        .string({
          required_error: t("common.form.leave.error.employee-id-required"),
        })
        .optional(),
      leave_type: z
        .string({
          required_error: t("common.form.leave.error.type-required"),
        })
        .refine(
          (val) => Object.values(LEAVE_TYPE).includes(val as LEAVE_TYPE),
          {
            message: t("common.form.leave.error.type-required"),
          },
        ),
      leave_duration: z.nativeEnum(LEAVE_DURATION),
      start_date: z.preprocess(
        (val) =>
          typeof val === "string" || val instanceof Date ? new Date(val) : val,
        z.date().refine(validators.validateDateFormat, {
          message: t("common.form.leave.error.invalid-date"),
        }),
      ),

      end_date: z
        .preprocess(
          (val) =>
            typeof val === "string" || val instanceof Date
              ? new Date(val)
              : val,
          z.date().refine(validators.validateDateFormat, {
            message: t("common.form.leave.error.invalid-date"),
          }),
        )

        .refine(validators.validateEndDateNotInPast, {
          message: t("common.form.leave.error.future-date"),
        }),
      reason: z
        .string()
        .min(3, {
          message: t("common.form.leave.error.reason-min"),
        })
        .max(500, {
          message: t("common.form.leave.error.reason-max"),
        }),
      // Validate individual files and total size of all files
      documents: z
        .array(attachmentFileSchema)
        .optional()
        .refine(
          (files) => {
            if (!files || files.length === 0) return true;
            // Calculate total size of all files
            const totalSize = files.reduce((sum, file) => sum + file.size, 0);
            // Check if total size exceeds MAX_FILE_SIZE
            return totalSize <= MAX_FILE_SIZE;
          },
          {
            message:
              t("common.form.attachments.error.totalMaxSize") ||
              "The total size of all files must be less than 5MB.",
          },
        ),
    })
    .refine(
      (data) => {
        const startDate = new Date(data.start_date);
        const endDate = new Date(data.end_date);
        return validators.validateStartBeforeEnd(startDate, endDate);
      },
      {
        message: t("common.form.leave.error.start-after-end"),
        path: ["end_date"],
      },
    );
};

// Schema for updating leave dates
export const leaveDateUpdateSchema = (t: TFunction) => {
  // Get current date and set to midnight
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Calculate date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
  ninetyDaysAgo.setHours(0, 0, 0, 0);

  return z
    .object({
      id: z.string().min(1, {
        message:
          t("common.form.leave.error.id-required") || "Leave ID is required",
      }),
      employeeId: z.string().min(1, {
        message:
          t("common.form.leave.error.employee-id-required") ||
          "Employee ID is required",
      }),
      startDate: z
        .date()
        .refine((date) => date !== null && date !== undefined, {
          message:
            t("common.form.leave.error.start-date-required") ||
            "Start date is required",
        })
        .refine(
          (date) => {
            const dateObj = new Date(date);
            return !isNaN(dateObj.getTime());
          },
          {
            message:
              t("common.form.leave.error.invalid-date") ||
              "Invalid date format",
          },
        )
        .refine(
          (date) => {
            const dateObj = new Date(date);
            return dateObj >= ninetyDaysAgo;
          },
          {
            message:
              t("common.form.leave.error.start-date-past") ||
              "Start date cannot be more than 3 months (90 days) in the past",
          },
        ),
      endDate: z
        .date()
        .refine((date) => date !== null && date !== undefined, {
          message:
            t("common.form.leave.error.end-date-required") ||
            "End date is required",
        })
        .refine(
          (date) => {
            const dateObj = new Date(date);
            return !isNaN(dateObj.getTime());
          },
          {
            message:
              t("common.form.leave.error.invalid-date") ||
              "Invalid date format",
          },
        ),
      leaveDuration: z
        .enum([
          LEAVE_DURATION.FULL_DAY,
          LEAVE_DURATION.HALF_DAY_MORNING,
          LEAVE_DURATION.HALF_DAY_AFTERNOON,
        ])
        .optional(),
      leaveType: z.nativeEnum(LEAVE_TYPE).optional(),
    })
    .refine(
      (data) => {
        const startDate = new Date(data.startDate);
        const endDate = new Date(data.endDate);
        return startDate <= endDate;
      },
      {
        message:
          t("common.form.leave.error.start-after-end") ||
          "Start date must be before end date",
        path: ["endDate"],
      },
    )
    .refine(
      (data) => {
        // Check if half-day leave has same start and end date
        const isHalfDay =
          data.leaveDuration === LEAVE_DURATION.HALF_DAY_MORNING ||
          data.leaveDuration === LEAVE_DURATION.HALF_DAY_AFTERNOON;

        if (isHalfDay) {
          const startDate = new Date(data.startDate);
          const endDate = new Date(data.endDate);
          return startDate.getTime() === endDate.getTime();
        }
        return true;
      },
      {
        message:
          t("common.form.leave.error.half-day-single-day") ||
          "Half-day leave must be for a single day (start date must equal end date)",
        path: ["endDate"],
      },
    )
    .refine(
      (data) => {
        // Check if certain leave types don't support half-day
        const isHalfDay =
          data.leaveDuration === LEAVE_DURATION.HALF_DAY_MORNING ||
          data.leaveDuration === LEAVE_DURATION.HALF_DAY_AFTERNOON;

        if (
          isHalfDay &&
          data.leaveType &&
          [
            LEAVE_TYPE.MARRIAGE,
            LEAVE_TYPE.MATERNITY,
            LEAVE_TYPE.PATERNITY,
          ].includes(data.leaveType)
        ) {
          return false;
        }
        return true;
      },
      {
        message:
          t("common.form.leave.error.marriage-no-half-day") ||
          "This leave type cannot be taken as half-day",
        path: ["leaveDuration"],
      },
    );
};

export type CreateLeaveSchemaType = z.infer<
  ReturnType<typeof createLeaveSchema>
>;

export type LeaveDateUpdateSchemaType = z.infer<
  ReturnType<typeof leaveDateUpdateSchema>
>;
