"use client";

import { Directions, LANGUAGES } from "@/constants/enum";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { ChartSkeleton } from "../../../../../../../components/skeletons";
import GenericBarChart from "../../../../../../../components/chart/bar-chart/generic-bar-chart";
import { TFunction } from "@/types";
import { CustomBar } from "../../../../../../../components/chart/bar-chart/custom-bar";
import { useAppointmentsData } from "../../../services/queries";
import { getMonthTranslations } from "@/constants/translations-mapping";
import useMediaQuery from "@/hooks/use-media-query";

const AppointmentsChart = () => {
  const lang = useLocale();
  const [mode, setMode] = useState("");
  const isMediumScreen = useMediaQuery("(max-width:667px)");
  const currentyear = new Date().getFullYear().toString();
  const effectiveMode = mode || currentyear;
  const t = useTranslations() as TFunction;
  const { appointmentsData, isError, isLoading } =
    useAppointmentsData(effectiveMode);
  // Set text direction based on language
  const textDirection = lang === LANGUAGES.ARABIC ? "rtl" : "ltr";
  const isAr = lang === LANGUAGES.ARABIC;
  const ticks = [0, 2, 4, 6, 8, 10];

  const xAxisTickFormatter = (value: string) =>
    textDirection === Directions.RTL
      ? getMonthTranslations(value, t) || value
      : value;

  const tooltipLabelFormatter = (label: any) => {
    return (
      <span className="text-[10px] font-semibold">
        {`${getMonthTranslations(label, t)} ${effectiveMode}`}
      </span>
    );
  };

  const tooltipFormatter = (value: any, name: any, payload: any) => {
    const label =
      name === "confirmed"
        ? t("cm.appointmentchart.confirmed")
        : name === "canceled"
        ? t("cm.appointmentchart.canceled")
        : name;
    return [
      <div className="flex justify-between w-full items-center">
        <span className="text-[10px]">{label}</span>
        <span className="text-xs text-black font-semibold">{value}</span>
      </div>,
    ];
  };

  if (isLoading) return <ChartSkeleton />;
  if (isError) {
    return (
      <div
        className="w-full mx-auto flex items-center justify-center bg-white p-4 rounded-2xl text-center border border-[#E5E6E6]"
        style={{ height: "407px" }}
      >
        <p className="text-error font-semibold">{isError.message}</p>
      </div>
    );
  }

  return (
    <div
      className={`w-full mx-auto bg-white rounded-2xl text-${textDirection} border border-[#E5E6E6]`}
    >
      <GenericBarChart
        title={t("cm.appointmentchart.appointments")}
        selectPlaceholder={t("cm.appointmentchart.annual")}
        selectOptions={[
          { value: "2023", label: "2023" },
          { value: "2024", label: "2024" },
          { value: "2025", label: "2025" },
        ]}
        legendLabels={[
          t("cm.appointmentchart.canceled"),
          t("cm.appointmentchart.confirmed"),
        ]}
        selectValue={mode}
        onSelectChange={(val) => setMode(val)}
        data={appointmentsData || []}
        chartContainerClass="h-[293px]"
        stackOffset="wiggle"
        margin={{
          top: 10,
          right: isAr ? -35 : 0,
          left: isAr ? 0 : -25,
          bottom: 0,
        }}
        ticks={ticks}
        xAxisTickFormatter={xAxisTickFormatter}
        tooltipFormatter={tooltipFormatter}
        tooltipLabelFormatter={tooltipLabelFormatter}
        xAxisKey={"month"}
        barSize={isMediumScreen ? 20 : 35.45}
        bars={[
          {
            stackId: "a",
            dataKey: "confirmed",
            fill: "hsl(var(--primary))",
            radius: [0, 0, 4, 4],
            shape: <CustomBar />,
          },
          {
            stackId: "a",
            dataKey: "canceled",
            fill: "hsl(var(--secondary))",
            radius: [4, 4, 0, 0],
            shape: <CustomBar />,
          },
        ]}
      />
    </div>
  );
};

export default AppointmentsChart;
