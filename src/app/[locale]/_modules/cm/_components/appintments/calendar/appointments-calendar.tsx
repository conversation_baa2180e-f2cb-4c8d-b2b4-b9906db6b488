"use client";

import { useState, useEffect } from "react";
import { useLocale } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import Calendar from "@/components/calendar";
import { EventData } from "../../../types/appointment-calendar";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";
import {Locale} from "@/i18n/routing";
const EventDetailsDialog = dynamic(() => import("./event-details-dialog"), {
  loading: () => (
    <Loader
      size={80}
      borderWidth={4}
      overlayColor="#000"
      overlayOpacity={0.5}
      overlayClassName="bg-opacity-50"
    />
  ),
  ssr: false,
});

// Sample event data for testing
const sampleEvents: EventData[] = [
  {
    id: "1",
    title: "اسم المريض",
    date: "2025-03-03",
    time: "08:00 - 09:00 صباحًا",
    statusTitle: "عنوان الحالة",
    status: "غضب باستمرار",
    descriptionTitle: "وصف الحالة",
    description:
      "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة. لقد تم توليد هذا النص من مولد النص العربي...",
    joinMeetingText: "انضم إلى الاجتماع",
    meetingLinkText: "رابط الاجتماع",
  },
];

export default function AppointmentsCalendar() {
  const locale: Locale = useLocale() as Locale;
  const isRtl = locale === LANGUAGES.ARABIC;
  const [selectedEvent, setSelectedEvent] = useState<EventData | null>(null);
  const [events, setEvents] = useState<EventData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    async function fetchEvents() {
      try {
        // Simulate a fetch delay
        setLoading(true);
        // For real API call, uncomment the lines below:
        // const response = await fetch("/api/appointments");
        // const data: EventData[] = await response.json();
        setEvents(sampleEvents);
      } catch (error) {
        console.error("Error fetching events:", error);
      } finally {
        setLoading(false);
      }
    }
    fetchEvents();
  }, []);

  return (
    <div>
      <Calendar
        events={events}
        locale={locale}
        isRtl={isRtl}
        loading={loading}
        onEventClick={(eventData) => setSelectedEvent(eventData)}
      />
      {selectedEvent && (
        <EventDetailsDialog
          selectedEvent={selectedEvent}
          onClose={() => setSelectedEvent(null)}
        />
      )}
    </div>
  );
}
