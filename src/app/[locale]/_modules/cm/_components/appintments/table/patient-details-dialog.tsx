"use client";

import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Appointment } from "../../../types/appointment-table";
import PatientStatus from "@/components/table/patient-status";
import { useTranslations } from "next-intl";
import { mapStatusToCanonical } from "@/constants/translations-mapping";
import { Separator } from "@radix-ui/react-select";
import ResponsiveDialog from "@/components/responsive-dialog";

type TPatientDetailsDialog = {
  showPatientDetails: boolean;
  setShowPatientDetails: React.Dispatch<React.SetStateAction<boolean>>;
  patientData: Appointment | null;
};
const PatientDetailsDialog = ({
  showPatientDetails,
  setShowPatientDetails,
  patientData,
}: TPatientDetailsDialog) => {
  return (
    <ResponsiveDialog
      open={showPatientDetails}
      onOpenChange={setShowPatientDetails}
      closeBtnStyle="top-[22px]"
      header={<BookingDetailsHeader patientData={patientData} />}
    >
      <BookingDetailsContent patientData={patientData} />
    </ResponsiveDialog>
  );
};

const BookingDetailsHeader = ({
  patientData,
}: {
  patientData: Appointment | null;
}) => {
  const t = useTranslations();
  const status = mapStatusToCanonical(patientData?.status ?? "waiting");
  return (
    <div className="text-right">
      <DialogTitle className="sr-only">تفاصيل الموعد</DialogTitle>
      <DialogDescription className="sr-only">
        تفاصيل الموعد القادم في 29 يناير 2025
      </DialogDescription>
      <div className="px-6 py-[22px] border-b ">
        <h2 className="!m-0 font-semibold text-base flex gap-1 items-center justify-start md:justify-between">
          <span className="shrink-0"> الموعد - 29 يناير، 2025</span>
          <span className="md:me-12">
            <PatientStatus
              status={status}
              label={t(`common.Table.body.patient-status.${status}`)}
            />
          </span>
        </h2>
      </div>
    </div>
  );
};
const BookingDetailsContent = ({
  patientData,
}: {
  patientData: Appointment | null;
}) => {
  return (
    <div className="text-right">
      <div className="space-y-4">
        <div className="flex flex-col border border-[#F2F4F7] rounded-[10px] py-[22px] px-[29.5px]">
          <h3 className="text-lg font-semibold ">المواعيد المحدد</h3>
          <Separator className="h-[1px] my-[13px] bg-[#E4E9EF]" />
          <div className="flex flex-row max-sm:flex-col justify-center gap-[6px]">
            <span className="bg-secondary text-white text-sm text-center font-medium w-full md:max-w-[330px] min-h-8 leading-8 max-h-8 rounded-[10px]">
              2025 يناير 29
            </span>
            <span className="bg-primary text-black text-sm text-center font-medium w-full md:max-w-[118px] rounded-[10px] min-h-8 leading-8 max-h-8 ">
              03:30 pm
            </span>
          </div>
        </div>
        <div className="flex flex-col border border-[#F2F4F7] rounded-[10px] py-[22px] px-[29.5px]">
          <h4 className="text-[#6B7280]">عنوان الحالة</h4>
          <p className="text-base font-normal leading-5">غضب باستمرار</p>
          <Separator className="h-[.5px] my-[13px] bg-[#E4E9EF]" />
          <h4 className="text-[#6B7280]">وصف الحالة</h4>
          <p className="text-base font-normal leading-5">
            هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة. لقد تم توليد هذا
            النص من مولد النص العربي، حيث يمكنك أن تولد مثل هذا النص أو العديد
            من النصوص الأخرى إضافة إلى زيادة عدد الحروف التي يولدها التطبيق.
          </p>
        </div>
        <div className="flex justify-between max-sm:flex-col mt-4 w-full gap-4 md:gap-6">
          <Button
            variant="ghost"
            className="w-full h-12 bg-error-600 text-white font-semibold"
          >
            إلغاء الحجز
          </Button>
          <Button variant="outline" className="w-full h-12 font-semibold">
            تغيير الموعد
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PatientDetailsDialog;
