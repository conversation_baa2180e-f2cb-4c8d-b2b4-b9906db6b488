export interface EventData {
  id: string;
  title: string;
  date: string;
  description?: string;
  time?: string;
  status?: string;
  statusTitle?: string;
  descriptionTitle?: string;
  joinMeetingText?: string;
  meetingLinkText?: string;
}

// Availability form
export type AvailabilityInterval = {
  from: string;
  to: string;
};

export type AvailabilityDay = {
  day: string;
  isEnabled: boolean;
  intervals: AvailabilityInterval[];
};

export type AvailabilityFormData = {
  availability: AvailabilityDay[];
};
