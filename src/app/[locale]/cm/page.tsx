import { Suspense } from "react";
import CardWrapper from "../_modules/cm/_components/cards/card-wrapper";
import {
  CardsSkeleton,
  ChartSkeleton,
  SkeletonTable,
} from "../../../components/skeletons";
import { AppointmentResponse } from "../_modules/cm/types/appointment-table";
import dynamic from "next/dynamic";

const AppointmentsChart = dynamic(
  () =>
    import("../_modules/cm/_components/appintments/chart/appointments-chart"),
  { loading: () => <ChartSkeleton /> },
);
const PatientsAppointmentsTable = dynamic(
  () =>
    import(
      "../_modules/cm/_components/appintments/table/patients-appointments-table"
    ),
  { loading: () => <SkeletonTable rowCount={3} /> },
);

async function getData(): Promise<AppointmentResponse> {
  // Sample data with 12 appointments for testing purposes.
  const appointments = [
    {
      id: "01",
      number: "01",
      patientName: "احمد علي",
      time: "29 يناير, 2025",
      description: "هذا النص هو مثال لنص",
      status: "incoming",
    },
    {
      id: "02",
      number: "02",
      patientName: "سارة محمد",
      time: "14 مارس, 2015",
      description: "هذا النص هو مثال لنص",
      status: "معلقة",
    },
    {
      id: "03",
      number: "03",
      patientName: "علي حسن",
      time: "30 أكتوبر, 2017",
      description: "مثال فقد",
      status: "انتظار",
    },
    {
      id: "04",
      number: "04",
      patientName: "خالد",
      time: "30 أكتوبر, 2017",
      description: "مثال فقد",
      status: "تمت",
    },
    {
      id: "05",
      number: "05",
      patientName: "ليلى أحمد",
      time: "15 فبراير, 2023",
      description: "تفاصيل الموعد",
      status: "incoming",
    },
    {
      id: "06",
      number: "06",
      patientName: "مريم يوسف",
      time: "20 ديسمبر, 2024",
      description: "معلومات إضافية",
      status: "incoming",
    },
    {
      id: "07",
      number: "07",
      patientName: "فاطمة علي",
      time: "05 يونيو, 2022",
      description: "موعد متابعة",
      status: "تمت",
    },
    {
      id: "08",
      number: "08",
      patientName: "عمرو خالد",
      time: "12 نوفمبر, 2023",
      description: "مراجعة الحالة",
      status: "انتظار",
    },
    {
      id: "09",
      number: "09",
      patientName: "ريم إبراهيم",
      time: "25 يوليو, 2023",
      description: "استشارة",
      status: "معلقة",
    },
    {
      id: "10",
      number: "10",
      patientName: "أحمد سمير",
      time: "18 أغسطس, 2022",
      description: "تحديث حالة",
      status: "incoming",
    },
    {
      id: "11",
      number: "11",
      patientName: "نور الهدى",
      time: "01 سبتمبر, 2024",
      description: "تفاصيل موعد",
      status: "incoming",
    },
    {
      id: "12",
      number: "12",
      patientName: "كريم سعيد",
      time: "22 أكتوبر, 2022",
      description: "معلومات عن الموعد",
      status: "تمت",
    },
  ];

  return { data: appointments, totalCount: appointments.length };
}

const CMSystem = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const appointmentsResponse = await getData();
  // Destructure the page and limit values from searchParams (with default values).
  const { page = "1", limit = "3" } = await searchParams;

  // Convert the string parameters to numbers.
  const currentPage = parseInt(page, 10);
  const pageLimit = parseInt(limit, 10);

  // Calculate start and end indices based on the current page and limit.
  const startIndex = (currentPage - 1) * pageLimit;
  const endIndex = startIndex + pageLimit;

  // Filter the data for the current page.
  const filteredData = appointmentsResponse.data.slice(startIndex, endIndex);

  // Prepare the filtered response with data and totalCount.
  const filteredAppointmentsResponse = {
    data: filteredData,
    totalCount: appointmentsResponse.totalCount,
  };
  return (
    <div className="max-md:pt-8 grid grid-cols-1 xl:grid-cols-[8.5fr_3.5fr] gap-5">
      <div className="h-full order-2 xl:order-1 shrink">
        <div className="grid gap-5 md:gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <Suspense fallback={<CardsSkeleton hasDetail={false} />}>
            <CardWrapper />
          </Suspense>
        </div>
        <div className="my-5">
          <AppointmentsChart />
        </div>
        <div className="pt-6 sm:pt-0">
          <PatientsAppointmentsTable
            showPagination={false}
            data={filteredAppointmentsResponse}
            searchParams={{ page, limit }}
          />
        </div>
      </div>
      <div className="border-2 h-full order-1 shrink-0 flex-[3.5]"></div>
    </div>
  );
};

export default CMSystem;
