import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 },
      );
    }

    const response = await peopleService.getEmployeeAttachments(id);
    if (!response || !response.data) {
      return NextResponse.json({
        data: [],
        meta: {
          pagination: {
            count: 0,
            page: page,
            limit: limit,
            from: 0,
            to: 0,
          },
        },
      });
    }

    const responseData = response;

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error in GET employee attachments route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const { attachmentId, newName } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 },
      );
    }

    if (!attachmentId) {
      return NextResponse.json(
        { error: "Missing attachment ID" },
        { status: 400 },
      );
    }

    if (!newName) {
      return NextResponse.json({ error: "Missing new name" }, { status: 400 });
    }

    try {
      const result = await peopleService.renameAttachment(
        id,
        attachmentId,
        newName,
      );

      return NextResponse.json({
        success: true,
        message: "Attachment renamed successfully",
        data: result,
      });
    } catch (renameError) {
      console.error("Error renaming attachment:", renameError);
      return NextResponse.json(
        { error: "Failed to rename attachment" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error in PUT employee attachment route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const attachmentId = searchParams.get("attachmentId");

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 },
      );
    }

    if (!attachmentId) {
      return NextResponse.json(
        { error: "Missing attachment ID" },
        { status: 400 },
      );
    }

    try {
      const result = await peopleService.deleteAttachment(id, attachmentId);

      return NextResponse.json({
        success: true,
        message: "Attachment deleted successfully",
        data: result,
      });
    } catch (deleteError) {
      console.error("Error deleting attachment:", deleteError);
      return NextResponse.json(
        { error: "Failed to delete attachment" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error in DELETE employee attachment route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
