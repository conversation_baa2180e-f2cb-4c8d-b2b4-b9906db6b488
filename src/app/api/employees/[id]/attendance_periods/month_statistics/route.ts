import { NextRequest, NextResponse } from "next/server";
import { EmployeeAttendanceStats } from "@/app/[locale]/_modules/people/type/employee";
import { peopleService } from "@/services/api/people";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 }
      );
    }

    try {
      const response = await peopleService.getEmployeeMonthStatistics(id);
      if (!response) {
        return NextResponse.json(
          { error: "No attendance data found" },
          { status: 404 }
        );
      }
      return NextResponse.json(response);
    } catch (error) {
      // Re-throw for the outer catch block to handle
      throw error;
    }
  } catch (error) {
    console.error("Error in GET employee attendance route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
