import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const search = searchParams.get("search") || "";

    const filterParams = new URLSearchParams();
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith("filter[") && !key.startsWith("group[")) {
        filterParams.append(key, value);
      }
    }

    const filters = filterParams.toString();

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);

    const sort = searchParams.get("sort") || "-start_date";

    const response = await peopleService.getEmployees(
      page,
      limit,
      sort,
      search,
      filters,
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching employees:", error);
    return NextResponse.json(
      { error: "Failed to fetch employees" },
      { status: 500 },
    );
  }
}
