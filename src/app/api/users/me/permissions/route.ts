import { NextRequest, NextResponse } from "next/server";
import { CoreAPI } from "@/services/api/core";
export async function GET(request: NextRequest) {
  try {
    const coreApi = new CoreAPI();
    const response = await coreApi.getPermissions();
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching system user data:", error);
    return NextResponse.json(
      { error: "Failed to fetch system user data" },
      { status: 500 }
    );
  }
}
