import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "20", 10);

    // Extract employee ID from the filter parameter
    const employeeId = searchParams.get("filter[employee_id_eq]") || "";

    try {
      const response = await peopleService.getSalaryCalculations(
        page,
        limit,
        employeeId,
      );

      // Return the response directly without wrapping it
      return NextResponse.json(response);
    } catch (error) {
      return NextResponse.json(
        { error: "Failed to fetch salary calculations" },
        { status: 500 },
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
