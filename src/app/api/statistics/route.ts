import { NextRequest, NextResponse } from "next/server";
import { coreAPI } from "@/services/api/core";
import { peopleService } from "@/services/api/people";

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const getParam = (key: string) => url.searchParams.get(key);

    const period = getParam("context[comparison_period]") || "";
    const id = getParam("context[employee_id]") || "";
    const start_date = getParam("context[start_date") || "";
    const end_date = getParam("context[end_date]") || "";
    const metricsArray = url.searchParams.getAll("filter[metric_key_in][]");
    if (!id) {
      return NextResponse.json(
        { error: "Missing employee_id" },
        { status: 400 },
      );
    }

    const response = await peopleService.getEmployeeStatistics(
      id,
      metricsArray,
      period,
      start_date,
      end_date,
    );
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching projects:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to fetch projects", message: errorMessage },
      { status: 500 },
    );
  }
}
