import { NextRequest, NextResponse } from "next/server";
import { coreAPI } from "@/services/api/core";

export async function GET(request: NextRequest) {
  try {
    const response = await coreAPI.getProjects();
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching projects:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to fetch projects", message: errorMessage },
      { status: 500 },
    );
  }
}
