import {NextRequest, NextResponse} from "next/server";
import {buildApiUrl} from "@/utils/api";

export async function GET(request: NextRequest) {
    try {
        const storeCookies = request.cookies;
        const coreToken = storeCookies.get("core_session_token")?.value;
        const selectedSystem = storeCookies.get("currSystem")?.value;

        if (!coreToken) {
            return NextResponse.json(
                {error: "No core token found"},
                {status: 401},
            );
        }

        // Fetch Global User (core)
        const coreResponse = await fetch(
            buildApiUrl("core", "/api/users/me"),
            {
                headers: {
                    Authorization: `Bearer ${coreToken}`,
                    "Content-Type": "application/json",
                },
            },
        );

        const globalUser = await coreResponse.json();

        let systemUser = null;

        // Fetch Selected System User if a system is selected
        if (selectedSystem) {
            const systemToken = storeCookies.get(
                `${selectedSystem}_session_token`,
            )?.value;

            if (systemToken) {
                const systemResponse = await fetch(
                    `https://${selectedSystem}.${process.env.BASE_API_URL}/api/users/me`,
                    {
                        headers: {
                            Authorization: `Bearer ${systemToken}`,
                            "Content-Type": "application/json",
                        },
                    },
                );

                systemUser = await systemResponse.json();
            }
        }

        return NextResponse.json({globalUser, systemUser});
    } catch (error) {
        console.error("Error fetching user data:", error);
        return NextResponse.json(
            {error: "Failed to fetch user data"},
            {status: 500},
        );
    }
}

// import { NextRequest, NextResponse } from "next/server";

// export async function GET(request: NextRequest) {
//   const storeCookies = await request.cookies;
//   const coreToken = storeCookies.get("core_session_token")?.value;
//   console.log(coreToken, console.log("coreToken from user route handler"));
//   const result = await fetch(
//     `https://core.${process.env.BASE_API_URL}/api/users/me`,
//     {
//       headers: {
//         Authorization: `Bearer ${coreToken}`,
//         "Content-Type": "application/json",
//       },
//     },
//   );
//   console.log(result, console.log("result from user route handler"));
//   const user = await result.json();
//   console.log(user, "user from route handler");
//   // console.log("the current user", user);
//   // if (result.redirect) {
//   //   return NextResponse.redirect(new URL(result.redirect, request.url));
//   // } else if (result.error) {
//   //   return NextResponse.json({ error: result.error }, { status: 500 });
//   // } else {
//   return NextResponse.json(user);
//   // }
// }
