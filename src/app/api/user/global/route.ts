import { CoreAPI } from "@/services/api/core";
import { redirect } from "next/navigation";
import { NextRequest, NextResponse } from "next/server";

// src/app/api/user/global/route.ts
export async function GET(_request: NextRequest) {
  try {
    const core = new CoreAPI();
    const globalUser = await core.getUser();

    console.log("from route handler", globalUser);

    // Return the user data in the expected structure
    // The CoreAPI.getUser() method now handles the transformation from the new API format
    return NextResponse.json({ user: globalUser });
  } catch (error: any) {
    console.error("Error fetching global user data:", error);
    if (error.isAuthError) {
      // Return a redirect response
      return redirect("http://localhost:3000/ar/auth/login");
    }

    console.error("Error fetching global user data:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
    // return NextResponse.json({ error: error }, { status: 500 });
  }
}
