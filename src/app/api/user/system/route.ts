import { NextRequest, NextResponse } from "next/server";
import { buildApiUrl } from "@/utils/api";
import { TSystems } from "@/types";

// src/app/api/user/system/route.ts
export async function GET(request: NextRequest) {
  try {
    const storeCookies = request.cookies;
    const selectedSystem = storeCookies.get("currSystem")?.value as TSystems;

    if (!selectedSystem) {
      return NextResponse.json(
        { error: "No system selected" },
        { status: 400 },
      );
    }

    const systemToken = storeCookies.get(
      `${selectedSystem}_session_token`,
    )?.value;

    if (!systemToken) {
      return NextResponse.json(
        { error: "No system token found" },
        { status: 401 },
      );
    }

    const systemResponse = await fetch(
      buildApiUrl(selectedSystem, "/api/users/me"),
      {
        headers: {
          Authorization: `Bearer ${systemToken}`,
          "Content-Type": "application/json",
        },
      },
    );

    const systemUser = await systemResponse.json();
    return NextResponse.json({ user: systemUser, system: selectedSystem });
  } catch (error) {
    console.error("Error fetching system user data:", error);
    return NextResponse.json(
      { error: "Failed to fetch system user data" },
      { status: 500 },
    );
  }
}
