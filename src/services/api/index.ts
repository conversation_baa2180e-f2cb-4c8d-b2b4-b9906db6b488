import { cookies } from "next/headers";
import { isTokenExpirationError } from "./auth-utils";

export class BaseAPI {
  constructor(protected baseURL: string) {}

  /**
   * Makes a request to the API with JSON data
   */
  protected async request<T>(path: string, options: RequestInit): Promise<T> {
    const cookieStore = await cookies();
    const lang = cookieStore.get("NEXT_LOCALE")?.value || "ar";

    const headers = {
      "Accept-Language": lang,
      "Content-Type": "application/json",
      ...(options.headers || {}),
    };

    return this.processRequest<T>(path, {
      ...options,
      headers,
    });
  }

  /**
   * Makes a request to the API with FormData
   */
  protected async requestWithFormData<T>(
    path: string,
    options: RequestInit & { body: FormData },
  ): Promise<T> {
    const cookieStore = await cookies();
    const lang = cookieStore.get("NEXT_LOCALE")?.value || "ar";

    const headers = {
      "Accept-Language": lang,
      // Don't set Content-Type for FormData
      ...(options.headers || {}),
    };

    return this.processRequest<T>(path, {
      ...options,
      headers,
    });
  }

  /**
   * Processes the request and handles common response logic
   */
  private async processRequest<T>(
    path: string,
    options: RequestInit,
  ): Promise<T> {
    // Log the API request as a curl command
    const curlParts = [
      "curl",
      "-X", options.method || "GET",
      `'${this.baseURL}/${path}'`,
    ];

    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        curlParts.push("-H", `'${key}: ${value}'`);
      });
    }

    if (options.body && typeof options.body === "string") {
      curlParts.push("--data", `'${options.body}'`);
    }

    console.debug("[API as curl]:", curlParts.join(" "));

    console.debug('------------------------------------ ------------------------------------');
    const res = await fetch(`${this.baseURL}/${path}`, options);

    if (!res.ok) {
      // If the response is not OK, throw an error with the status
      const errorData = await res.json().catch(() => ({}));
      (errorData as any).status = res.status;
      (errorData as any).data = errorData;
      const error = new Error(
        errorData.error || errorData.errors[0].detail || res.statusText,
      );

      // (error as any).status = res.status;
      // (error as any).data = errorData;
      //
      //
      //   isTokenExpirationError(res.status, error.message),
      //   "isTokenExpirationError",
      // );

      // // Handle token expiration
      // if (isTokenExpirationError(res.status, error.message)) {
      //   const cookieStore = await cookies();
      //   const lang = cookieStore.get("NEXT_LOCALE")?.value || "en";

      //   cookieStore.delete("core_session_token");
      //   cookieStore.delete("currSystem");

      //   // Clear system tokens
      //   const systems = ["people", "procure", "cm"];
      //   systems.forEach((system) => {
      //     cookieStore.delete(`${system}_session_token`);
      //   });

      //   // In server components/actions, we can use redirect
      //   (error as any).isAuthError = true;
      //   (error as any).redirectUrl = `/${lang}/auth/login`;
      // }
      throw error;
    }

    // Parse the response as JSON
    const data = await res.json();

    // If the API doesn't return data, throw an error
    if (!data) {
      const error = new Error("No data returned from API");
      (error as any).status = res.status;
      throw error;
    }

    return data as T;
  }
}

export const getCoreSessionToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();

  return cookieStore.get("core_session_token")?.value || null;
};

export const getMainToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get("main_token")?.value || null;
};

export const getUserLanguage = async (): Promise<string> => {
  const cookieStore = await cookies();
  return cookieStore.get("NEXT_LOCALE")?.value || "ar";
};

export const getCurrSystem = async (): Promise<string> => {
  const cookieStore = await cookies();
  return cookieStore.get("currSystem")?.value || "core";
};
